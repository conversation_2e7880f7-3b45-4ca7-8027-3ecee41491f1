#!/usr/bin/env python3
"""
Comprehensive test suite for MultiPersonKeypointModel
Tests model logic for multi-person scenarios including loss computation and heatmap generation.
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import unittest
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "keypoint-detection"))

from dll.models.keypoint_model import MultiPersonKeypointModel
from dll.configs.model_config import ModelConfig, BackboneConfig, HeatmapHeadConfig, PersonDetectionConfig, KeypointHeadConfig
from dll.configs.training_config import TrainingConfig, OptimizerConfig, LossConfig
from dll.losses.keypoint_loss import KeypointLoss

class TestMultiPersonKeypointModel(unittest.TestCase):
    """Test suite for MultiPersonKeypointModel"""

    @classmethod
    def setUpClass(cls):
        """Set up test configurations and model"""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Running tests on device: {cls.device}")

        # Create test configurations
        cls.backbone_config = BackboneConfig(
            width_mult=1.0,
            in_channels=1,  # Grayscale
            out_channels=128,
            input_size=224
        )

        cls.person_head_config = PersonDetectionConfig(
            in_channels=128,
            num_classes=1,
            conf_threshold=0.3,
            nms_iou_threshold=0.3
        )

        cls.keypoint_head_config = KeypointHeadConfig(
            in_channels=128,
            num_keypoints=17,
            height=56,
            width=56,
            fine_branch_channels=64,
            regression_channels=32,
            visibility_channels=32,
            dropout_rate=0.2
        )

        cls.heatmap_head_config = HeatmapHeadConfig(
            in_channels=64,
            hidden_channels=64,
            num_keypoints=17,
            heatmap_size=(56, 56)
        )

        cls.model_config = ModelConfig(
            backbone=cls.backbone_config,
            person_head=cls.person_head_config,
            keypoint_head=cls.keypoint_head_config,
            heatmap_head=cls.heatmap_head_config,
            num_keypoints=17
        )

        cls.training_config = TrainingConfig(
            batch_size=2,
            num_epochs=10,
            optimizer=OptimizerConfig(learning_rate=0.001),
            loss=LossConfig(
                keypoint_loss_weight=1.0,
                visibility_loss_weight=1.0
            ),
            pck_thresholds=[0.05, 0.1, 0.2]
        )

        # Create model
        cls.model = MultiPersonKeypointModel(cls.model_config, cls.training_config).to(cls.device)
        cls.model.eval()

    def create_test_batch(self, batch_size=2, num_persons_per_image=None):
        """Create test batch with multiple persons"""
        if num_persons_per_image is None:
            num_persons_per_image = [2, 3]  # Different number of persons per image

        # Ensure we have enough person counts
        while len(num_persons_per_image) < batch_size:
            num_persons_per_image.append(1)

        max_persons = max(num_persons_per_image[:batch_size])

        # Create images
        images = torch.randn(batch_size, 1, 224, 224, device=self.device)

        # Create bboxes (list of tensors)
        bboxes = []
        for i in range(batch_size):
            num_persons = num_persons_per_image[i]
            # Generate random bboxes in normalized format [cx, cy, w, h]
            batch_bboxes = torch.rand(num_persons, 4, device=self.device)
            batch_bboxes[:, 2:] = batch_bboxes[:, 2:] * 0.5 + 0.2  # Width and height between 0.2-0.7
            bboxes.append(batch_bboxes)

        # Create keypoints and visibilities
        keypoints = torch.zeros(batch_size, max_persons, 17, 2, device=self.device)
        visibilities = torch.zeros(batch_size, max_persons, 17, device=self.device)

        for i in range(batch_size):
            num_persons = num_persons_per_image[i]
            # Random keypoints in normalized coordinates
            keypoints[i, :num_persons] = torch.rand(num_persons, 17, 2, device=self.device)
            # Random visibilities (0, 1, or 2)
            visibilities[i, :num_persons] = torch.randint(0, 3, (num_persons, 17), device=self.device).float()

        # Create heatmaps from keypoints
        heatmaps = self._create_heatmaps_from_keypoints(keypoints, visibilities)

        return {
            'image': images,
            'bboxes': bboxes,
            'keypoints': keypoints,
            'visibilities': visibilities,
            'heatmaps': heatmaps
        }

    def _create_heatmaps_from_keypoints(self, keypoints, visibilities, heatmap_size=(56, 56), sigma=2.0):
        """Create ground truth heatmaps from keypoints"""
        B, P, K, _ = keypoints.shape
        H, W = heatmap_size
        device = keypoints.device

        heatmaps = torch.zeros(B, K, H, W, device=device)

        # Create coordinate grids
        y_grid, x_grid = torch.meshgrid(
            torch.arange(H, device=device, dtype=torch.float32),
            torch.arange(W, device=device, dtype=torch.float32),
            indexing='ij'
        )

        # Convert normalized keypoints to pixel coordinates
        keypoints_px = keypoints.clone()
        keypoints_px[..., 0] *= W
        keypoints_px[..., 1] *= H

        for b in range(B):
            for k in range(K):
                heatmap = torch.zeros(H, W, device=device)

                for p in range(P):
                    if visibilities[b, p, k] > 0:
                        x, y = keypoints_px[b, p, k]

                        # Generate Gaussian
                        dx = x_grid - x
                        dy = y_grid - y
                        dist_sq = dx * dx + dy * dy
                        gaussian = torch.exp(-dist_sq / (2 * sigma * sigma))

                        # Use max operation to handle overlapping persons
                        heatmap = torch.maximum(heatmap, gaussian)

                heatmaps[b, k] = heatmap

        return heatmaps

    def test_model_forward_single_person(self):
        """Test model forward pass with single person per image"""
        print("\n=== Testing Single Person Forward Pass ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[1, 1])

        with torch.no_grad():
            outputs = self.model(batch)

        # Check output structure
        self.assertIn('keypoints', outputs)
        self.assertIn('visibilities', outputs)
        self.assertIn('heatmap', outputs)
        self.assertIn('boxes', outputs)

        # Check shapes
        self.assertEqual(outputs['keypoints'].shape[0], 2)  # Batch size
        self.assertEqual(outputs['keypoints'].shape[-1], 2)  # x, y coordinates
        self.assertEqual(outputs['visibilities'].shape[-1], 17)  # 17 keypoints

        print(f"✓ Keypoints shape: {outputs['keypoints'].shape}")
        print(f"✓ Visibilities shape: {outputs['visibilities'].shape}")
        print(f"✓ Heatmap shape: {outputs['heatmap'].shape}")

    def test_model_forward_multiple_persons(self):
        """Test model forward pass with multiple persons per image"""
        print("\n=== Testing Multiple Persons Forward Pass ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[2, 3])

        with torch.no_grad():
            outputs = self.model(batch)

        # Check that model handles multiple persons correctly
        self.assertIn('keypoints', outputs)
        self.assertIn('visibilities', outputs)

        # Check that max persons is handled correctly
        max_persons = max([len(bbox) for bbox in batch['bboxes']])
        self.assertLessEqual(outputs['keypoints'].shape[1], max_persons)

        print(f"✓ Handled {max_persons} max persons per image")
        print(f"✓ Output keypoints shape: {outputs['keypoints'].shape}")

    def test_loss_computation_single_person(self):
        """Test loss computation for single person scenario"""
        print("\n=== Testing Loss Computation (Single Person) ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[1, 1])

        # Forward pass with loss computation
        outputs = self.model(batch)

        # Check that loss is computed
        self.assertIn('loss', outputs)
        self.assertIsInstance(outputs['loss'], torch.Tensor)
        self.assertEqual(outputs['loss'].dim(), 0)  # Scalar loss

        loss_value = outputs['loss'].item()
        self.assertGreater(loss_value, 0)  # Loss should be positive
        self.assertLess(loss_value, 1000)  # Loss should be reasonable

        print(f"✓ Loss computed: {loss_value:.4f}")

    def test_loss_computation_multiple_persons(self):
        """Test loss computation for multiple persons scenario"""
        print("\n=== Testing Loss Computation (Multiple Persons) ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[2, 3])

        # Forward pass with loss computation
        outputs = self.model(batch)

        # Check that loss is computed correctly for multiple persons
        self.assertIn('loss', outputs)
        loss_value = outputs['loss'].item()

        self.assertGreater(loss_value, 0)
        self.assertLess(loss_value, 1000)

        print(f"✓ Multi-person loss computed: {loss_value:.4f}")

    def test_heatmap_generation(self):
        """Test heatmap generation logic"""
        print("\n=== Testing Heatmap Generation ===")

        batch = self.create_test_batch(batch_size=1, num_persons_per_image=[2])

        # Test the internal heatmap conversion method
        keypoints = batch['keypoints']
        visibilities = batch['visibilities']

        heatmaps = self.model._convert_keypoints_to_heatmaps(
            keypoints, visibilities, heatmap_size=(56, 56)
        )

        # Check heatmap properties
        self.assertEqual(heatmaps.shape, (1, 17, 56, 56))
        self.assertGreaterEqual(heatmaps.min().item(), 0)
        self.assertLessEqual(heatmaps.max().item(), 1)

        # Check that visible keypoints generate non-zero heatmaps
        for k in range(17):
            if visibilities[0, :, k].sum() > 0:  # If any person has this keypoint visible
                self.assertGreater(heatmaps[0, k].sum().item(), 0)

        print(f"✓ Heatmaps shape: {heatmaps.shape}")
        print(f"✓ Heatmap value range: [{heatmaps.min().item():.4f}, {heatmaps.max().item():.4f}]")

    def test_training_step(self):
        """Test training step functionality"""
        print("\n=== Testing Training Step ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[1, 2])

        # Create optimizer
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)

        # Set model to training mode
        self.model.train()

        # Perform training step
        metrics = self.model.train_step(batch, optimizer)

        # Check metrics
        self.assertIn('loss', metrics)
        self.assertIn('keypoint_loss', metrics)
        self.assertIsInstance(metrics['loss'], float)

        print(f"✓ Training metrics: {metrics}")

        # Set back to eval mode
        self.model.eval()

    def test_validation_step(self):
        """Test validation step functionality"""
        print("\n=== Testing Validation Step ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[1, 2])

        # Perform validation step
        metrics = self.model.validate_step(batch)

        # Check metrics
        self.assertIn('loss', metrics)
        self.assertIn('avg_ADE', metrics)
        self.assertIn('pck_0.05', metrics)

        print(f"✓ Validation metrics: {metrics}")

    def test_loss_components(self):
        """Test individual loss components"""
        print("\n=== Testing Loss Components ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[2, 1])

        # Forward pass
        outputs = self.model(batch)

        # Check individual loss components
        if 'heatmap_loss' in outputs:
            self.assertIsInstance(outputs['heatmap_loss'], (float, torch.Tensor))
            print(f"✓ Heatmap loss: {outputs['heatmap_loss']}")

        if 'total_loss' in outputs:
            self.assertIsInstance(outputs['total_loss'], (float, torch.Tensor))
            print(f"✓ Total loss: {outputs['total_loss']}")

    def test_metrics_calculation(self):
        """Test metrics calculation (PCK, ADE)"""
        print("\n=== Testing Metrics Calculation ===")

        batch = self.create_test_batch(batch_size=2, num_persons_per_image=[1, 2])

        # Forward pass
        outputs = self.model(batch)

        # Test metrics calculation manually
        if 'keypoints' in outputs and 'keypoints' in batch:
            metrics = self.model._calculate_metrics(outputs, batch)

            self.assertIn('avg_ADE', metrics)
            self.assertIn('pck_0.05', metrics)

            # Check metric ranges
            self.assertGreaterEqual(metrics['avg_ADE'], 0)
            self.assertGreaterEqual(metrics['pck_0.05'], 0)
            self.assertLessEqual(metrics['pck_0.05'], 1)

            print(f"✓ Metrics calculated: {metrics}")

    def test_heatmap_to_keypoint_conversion(self):
        """Test heatmap to keypoint conversion"""
        print("\n=== Testing Heatmap to Keypoint Conversion ===")

        # Create synthetic heatmaps
        batch_size, num_keypoints = 1, 17
        heatmap_size = (56, 56)

        # Create heatmaps with known peaks
        heatmaps = torch.zeros(batch_size, num_keypoints, *heatmap_size, device=self.device)

        # Add Gaussian peaks at known locations
        peak_locations = [(28, 28), (14, 42), (42, 14)]  # Some test locations
        for i, (y, x) in enumerate(peak_locations[:num_keypoints]):
            if i < num_keypoints:
                # Create Gaussian peak
                y_grid, x_grid = torch.meshgrid(
                    torch.arange(heatmap_size[0], device=self.device),
                    torch.arange(heatmap_size[1], device=self.device),
                    indexing='ij'
                )
                dist_sq = (x_grid - x)**2 + (y_grid - y)**2
                heatmaps[0, i] = torch.exp(-dist_sq / (2 * 2**2))

        # Test decode_heatmap method
        keypoints, visibilities = self.model.decode_heatmap(heatmaps)

        # Check shapes
        self.assertEqual(keypoints.shape, (batch_size, num_keypoints, 2))
        self.assertEqual(visibilities.shape, (batch_size, num_keypoints))

        # Check that detected keypoints are close to expected locations
        for i, (y, x) in enumerate(peak_locations[:3]):
            detected_x = keypoints[0, i, 0].item() * (heatmap_size[1] - 1)
            detected_y = keypoints[0, i, 1].item() * (heatmap_size[0] - 1)

            self.assertAlmostEqual(detected_x, x, delta=2.0)
            self.assertAlmostEqual(detected_y, y, delta=2.0)

        print(f"✓ Keypoint detection accuracy verified")
        print(f"✓ Detected keypoints shape: {keypoints.shape}")

    def test_edge_cases(self):
        """Test edge cases and error handling"""
        print("\n=== Testing Edge Cases ===")

        # Test with no persons
        batch_no_persons = {
            'image': torch.randn(1, 1, 224, 224, device=self.device),
            'bboxes': [torch.zeros(0, 4, device=self.device)],
            'keypoints': torch.zeros(1, 0, 17, 2, device=self.device),
            'visibilities': torch.zeros(1, 0, 17, device=self.device),
            'heatmaps': torch.zeros(1, 17, 56, 56, device=self.device)
        }

        with torch.no_grad():
            outputs = self.model(batch_no_persons)

        self.assertIn('keypoints', outputs)
        print("✓ Handled no-person case")

        # Test with very small image
        try:
            small_batch = {
                'image': torch.randn(1, 1, 32, 32, device=self.device),
                'bboxes': [torch.tensor([[0.5, 0.5, 0.3, 0.3]], device=self.device)],
            }
            with torch.no_grad():
                outputs = self.model(small_batch)
            print("✓ Handled small image case")
        except Exception as e:
            print(f"⚠ Small image case failed (expected): {e}")

    def test_coordinate_conversion(self):
        """Test coordinate conversion from ROI to original image"""
        print("\n=== Testing Coordinate Conversion ===")

        # Test convert_to_original_coords method
        # Create test keypoints in ROI space (normalized [0,1])
        roi_keypoints = torch.tensor([[[0.5, 0.5], [0.0, 0.0], [1.0, 1.0]]], device=self.device)  # [1, 3, 2]

        # Test bounding box (normalized [cx, cy, w, h])
        bbox = torch.tensor([0.5, 0.5, 0.4, 0.6], device=self.device)  # Center at (0.5, 0.5), size 0.4x0.6

        # Convert to original coordinates
        original_keypoints = self.model.convert_to_original_coords(roi_keypoints, bbox)

        # Check shapes
        self.assertEqual(original_keypoints.shape, (1, 3, 2))

        # Check that coordinates are within [0, 1]
        self.assertTrue(torch.all(original_keypoints >= 0))
        self.assertTrue(torch.all(original_keypoints <= 1))

        # Check specific conversions
        # ROI center (0.5, 0.5) should map to bbox center (0.5, 0.5)
        center_point = original_keypoints[0, 0]  # First keypoint at ROI center
        expected_center = torch.tensor([0.5, 0.5], device=self.device)
        self.assertTrue(torch.allclose(center_point, expected_center, atol=1e-6))

        print(f"✓ Coordinate conversion verified")
        print(f"✓ Original keypoints: {original_keypoints}")

    def test_batch_consistency(self):
        """Test that model produces consistent results across different batch sizes"""
        print("\n=== Testing Batch Consistency ===")

        # Create identical inputs with different batch sizes
        single_image = torch.randn(1, 1, 224, 224, device=self.device)
        single_bbox = [torch.tensor([[0.5, 0.5, 0.3, 0.3]], device=self.device)]

        # Test single batch
        batch_1 = {'image': single_image, 'bboxes': single_bbox}

        # Test batch of 2 with identical images
        batch_2 = {
            'image': single_image.repeat(2, 1, 1, 1),
            'bboxes': single_bbox * 2
        }

        with torch.no_grad():
            outputs_1 = self.model(batch_1)
            outputs_2 = self.model(batch_2)

        # Check that first element of batch_2 is similar to batch_1
        # (allowing for small numerical differences)
        if 'keypoints' in outputs_1 and 'keypoints' in outputs_2:
            kpts_1 = outputs_1['keypoints'][0]
            kpts_2 = outputs_2['keypoints'][0]

            # Should be close but not necessarily identical due to batch norm, etc.
            max_diff = torch.max(torch.abs(kpts_1 - kpts_2)).item()
            self.assertLess(max_diff, 0.1)  # Allow some difference

            print(f"✓ Batch consistency verified (max diff: {max_diff:.6f})")

        print("✓ Model produces consistent results across batch sizes")

def run_performance_test():
    """Run performance benchmarks"""
    print("\n" + "="*60)
    print("PERFORMANCE BENCHMARKS")
    print("="*60)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Create model
    model_config = ModelConfig(
        backbone=BackboneConfig(in_channels=1, out_channels=128),
        heatmap_head=HeatmapHeadConfig(in_channels=64, num_keypoints=17),
        num_keypoints=17
    )
    training_config = TrainingConfig(pck_thresholds=[0.05, 0.1, 0.2])

    model = MultiPersonKeypointModel(model_config, training_config).to(device)
    model.eval()

    # Test different batch sizes and person counts
    test_cases = [
        (1, [1]),      # Single image, single person
        (2, [1, 2]),   # Two images, 1 and 2 persons
        (4, [1, 2, 3, 2]),  # Four images, varying persons
    ]

    for batch_size, num_persons_list in test_cases:
        # Create test batch
        images = torch.randn(batch_size, 1, 224, 224, device=device)
        bboxes = []
        for num_persons in num_persons_list:
            batch_bboxes = torch.rand(num_persons, 4, device=device)
            batch_bboxes[:, 2:] *= 0.5
            batch_bboxes[:, 2:] += 0.2
            bboxes.append(batch_bboxes)

        batch = {'image': images, 'bboxes': bboxes}

        # Measure inference time
        import time
        torch.cuda.synchronize() if device.type == 'cuda' else None

        start_time = time.time()
        with torch.no_grad():
            for _ in range(10):  # Run 10 times for average
                outputs = model(batch)
        torch.cuda.synchronize() if device.type == 'cuda' else None

        avg_time = (time.time() - start_time) / 10
        total_persons = sum(num_persons_list)

        print(f"Batch size {batch_size}, {total_persons} total persons: {avg_time:.4f}s avg")

if __name__ == "__main__":
    print("="*60)
    print("MULTIPERSON KEYPOINT MODEL TEST SUITE")
    print("="*60)

    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)

    # Run performance tests
    run_performance_test()

    print("\n" + "="*60)
    print("ALL TESTS COMPLETED")
    print("="*60)
