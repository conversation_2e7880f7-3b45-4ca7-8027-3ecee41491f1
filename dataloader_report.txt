================================================================================
DATALOADER OUTPUT ANALYSIS REPORT
================================================================================
Generated: 2025-05-24 19:21:28
Dataset Path: D:/AI/Keypoint_model/minidatasets

1. DATASET INFORMATION
----------------------------------------
• Total samples: 9
• Image size: 224x224
• Grayscale: True
• Number of keypoints: 17
• Heatmap size: 56x56
• Max persons per image: 5

2. SINGLE SAMPLE ANALYSIS
----------------------------------------
Sample Structure:
  • image: torch.Size([1, 224, 224]) (torch.float32)
    - Range: [-0.9922, 0.8275]
    - Mean: -0.3849
    - Non-zero elements: 50176
  • heatmaps: torch.Size([1, 17, 56, 56]) (torch.float32)
    - Range: [0.0000, 0.0399]
    - Mean: 0.0002
    - Non-zero elements: 1313
  • visibilities: torch.Size([1, 1, 17]) (torch.float32)
    - Range: [0.0000, 2.0000]
    - Mean: 0.4706
    - Non-zero elements: 4
  • bboxes: torch.Size([1, 4]) (torch.float32)
    - Range: [0.5000, 1.0000]
    - Mean: 0.7397
    - Non-zero elements: 4
  • num_persons: int
  • img_path: str
  • orig_size: tuple

3. BATCH ANALYSIS
----------------------------------------
• Batch size: 4
• Number of batches: 3

Batch Structure:
  • image: torch.Size([4, 1, 224, 224]) (torch.float32)
  • heatmaps: torch.Size([4, 1, 17, 56, 56]) (torch.float32)
  • visibilities: torch.Size([4, 1, 17]) (torch.float32)
  • bboxes: List[1]
    - Element shape: torch.Size([4, 1, 4])
  • num_persons: torch.Size([4]) (torch.int64)
  • img_path: List[4]
  • orig_size: List[4]

4. KEYPOINT ANALYSIS
----------------------------------------
Heatmaps:
  • Shape: torch.Size([1, 17, 56, 56])
  • Active heatmaps: 17
Visibilities:
  • Shape: torch.Size([17])
  • Visible keypoints: 4
  • Unique values: [0.0, 2.0]
  • Visible indices: [0, 1, 3, 6]

5. DATA CONSISTENCY CHECK
----------------------------------------
Checked 9 samples:
  ✓ image: Consistent shape torch.Size([1, 224, 224])
  ✓ heatmaps: Consistent shape torch.Size([1, 17, 56, 56])
  ⚠ visibilities: Inconsistent shapes [torch.Size([1, 3, 17]), torch.Size([1, 1, 17]), torch.Size([1, 2, 17])]
  ⚠ bboxes: Inconsistent shapes [torch.Size([2, 4]), torch.Size([3, 4]), torch.Size([1, 4])]
  • Average persons per image: 1.00
  • Person count range: [1, 1]
  ✓ No errors encountered

6. PERFORMANCE METRICS
----------------------------------------
  • Average single sample loading time: 0.0135s
  • Batch loading time: 0.0468s
  • Estimated throughput: 85.42 samples/sec

7. RECOMMENDATIONS
----------------------------------------
• Fix shape inconsistencies in: visibilities, bboxes

================================================================================
END OF REPORT
================================================================================