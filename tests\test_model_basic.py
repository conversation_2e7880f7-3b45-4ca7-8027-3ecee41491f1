#!/usr/bin/env python3
"""
Basic test for MultiPersonKeypointModel without loss computation
Tests core functionality and multi-person handling.
"""

import os
import sys
import torch
import numpy as np
import unittest
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "keypoint-detection"))

from dll.models.keypoint_model import MultiPersonKeypointModel
from dll.configs.model_config import ModelConfig, BackboneConfig, HeatmapHeadConfig, PersonDetectionConfig, KeypointHeadConfig
from dll.configs.training_config import TrainingConfig, OptimizerConfig, LossConfig

class TestModelBasic(unittest.TestCase):
    """Basic tests for MultiPersonKeypointModel"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test configurations and model"""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Running tests on device: {cls.device}")
        
        # Create test configurations
        cls.model_config = ModelConfig(
            backbone=BackboneConfig(
                in_channels=1,  # Grayscale
                out_channels=128,
                input_size=224
            ),
            person_head=PersonDetectionConfig(
                in_channels=128,
                num_classes=1
            ),
            keypoint_head=KeypointHeadConfig(
                in_channels=128,
                num_keypoints=17,
                height=56,
                width=56
            ),
            heatmap_head=HeatmapHeadConfig(
                in_channels=64,
                hidden_channels=64,
                num_keypoints=17,
                heatmap_size=(56, 56)
            ),
            num_keypoints=17
        )
        
        cls.training_config = TrainingConfig(
            batch_size=2,
            num_epochs=10,
            optimizer=OptimizerConfig(learning_rate=0.001),
            loss=LossConfig(),
            pck_thresholds=[0.05, 0.1, 0.2]
        )
        
        # Create model
        cls.model = MultiPersonKeypointModel(cls.model_config, cls.training_config).to(cls.device)
        cls.model.eval()
    
    def create_test_batch_inference_only(self, batch_size=2, num_persons_per_image=None):
        """Create test batch for inference only (no ground truth)"""
        if num_persons_per_image is None:
            num_persons_per_image = [2, 3]
        
        # Ensure we have enough person counts
        while len(num_persons_per_image) < batch_size:
            num_persons_per_image.append(1)
        
        # Create images
        images = torch.randn(batch_size, 1, 224, 224, device=self.device)
        
        # Create bboxes (list of tensors)
        bboxes = []
        for i in range(batch_size):
            num_persons = num_persons_per_image[i]
            # Generate random bboxes in normalized format [cx, cy, w, h]
            batch_bboxes = torch.rand(num_persons, 4, device=self.device)
            batch_bboxes[:, 2:] = batch_bboxes[:, 2:] * 0.5 + 0.2  # Width and height between 0.2-0.7
            bboxes.append(batch_bboxes)
        
        return {
            'image': images,
            'bboxes': bboxes
        }
    
    def test_model_forward_inference_single_person(self):
        """Test model forward pass for inference with single person"""
        print("\n=== Testing Single Person Inference ===")
        
        batch = self.create_test_batch_inference_only(batch_size=2, num_persons_per_image=[1, 1])
        
        with torch.no_grad():
            outputs = self.model(batch)
        
        # Check output structure
        self.assertIn('keypoints', outputs)
        self.assertIn('visibilities', outputs)
        self.assertIn('heatmap', outputs)
        self.assertIn('boxes', outputs)
        
        # Check shapes
        self.assertEqual(outputs['keypoints'].shape[0], 2)  # Batch size
        self.assertEqual(outputs['keypoints'].shape[-1], 2)  # x, y coordinates
        self.assertEqual(outputs['visibilities'].shape[-1], 17)  # 17 keypoints
        
        print(f"✓ Keypoints shape: {outputs['keypoints'].shape}")
        print(f"✓ Visibilities shape: {outputs['visibilities'].shape}")
        print(f"✓ Heatmap shape: {outputs['heatmap'].shape}")
        print(f"✓ Boxes type: {type(outputs['boxes'])}")
        
    def test_model_forward_inference_multiple_persons(self):
        """Test model forward pass for inference with multiple persons"""
        print("\n=== Testing Multiple Persons Inference ===")
        
        batch = self.create_test_batch_inference_only(batch_size=2, num_persons_per_image=[2, 3])
        
        with torch.no_grad():
            outputs = self.model(batch)
        
        # Check that model handles multiple persons correctly
        self.assertIn('keypoints', outputs)
        self.assertIn('visibilities', outputs)
        
        # Check that max persons is handled correctly
        max_persons = max([len(bbox) for bbox in batch['bboxes']])
        self.assertLessEqual(outputs['keypoints'].shape[1], max_persons)
        
        print(f"✓ Handled {max_persons} max persons per image")
        print(f"✓ Output keypoints shape: {outputs['keypoints'].shape}")
        print(f"✓ Output visibilities shape: {outputs['visibilities'].shape}")
        
    def test_heatmap_generation_internal(self):
        """Test internal heatmap generation method"""
        print("\n=== Testing Internal Heatmap Generation ===")
        
        # Create test keypoints and visibilities
        batch_size, max_persons, num_keypoints = 2, 3, 17
        keypoints = torch.rand(batch_size, max_persons, num_keypoints, 2, device=self.device)
        visibilities = torch.randint(0, 3, (batch_size, max_persons, num_keypoints), device=self.device).float()
        
        # Test the internal heatmap conversion method
        heatmaps = self.model._convert_keypoints_to_heatmaps(
            keypoints, visibilities, heatmap_size=(56, 56)
        )
        
        # Check heatmap properties
        self.assertEqual(heatmaps.shape, (batch_size, num_keypoints, 56, 56))
        self.assertGreaterEqual(heatmaps.min().item(), 0)
        self.assertLessEqual(heatmaps.max().item(), 1)
        
        print(f"✓ Heatmaps shape: {heatmaps.shape}")
        print(f"✓ Heatmap value range: [{heatmaps.min().item():.4f}, {heatmaps.max().item():.4f}]")
        
    def test_decode_heatmap_method(self):
        """Test heatmap decoding method"""
        print("\n=== Testing Heatmap Decoding ===")
        
        # Create synthetic heatmaps with known peaks
        batch_size, num_keypoints = 1, 17
        heatmap_size = (56, 56)
        
        heatmaps = torch.zeros(batch_size, num_keypoints, *heatmap_size, device=self.device)
        
        # Add Gaussian peaks at known locations
        peak_locations = [(28, 28), (14, 42), (42, 14)]
        for i, (y, x) in enumerate(peak_locations[:3]):
            # Create Gaussian peak
            y_grid, x_grid = torch.meshgrid(
                torch.arange(heatmap_size[0], device=self.device),
                torch.arange(heatmap_size[1], device=self.device),
                indexing='ij'
            )
            dist_sq = (x_grid - x)**2 + (y_grid - y)**2
            heatmaps[0, i] = torch.exp(-dist_sq / (2 * 2**2))
        
        # Test decode_heatmap method
        keypoints, visibilities = self.model.decode_heatmap(heatmaps)
        
        # Check shapes
        self.assertEqual(keypoints.shape, (batch_size, num_keypoints, 2))
        self.assertEqual(visibilities.shape, (batch_size, num_keypoints))
        
        # Check that detected keypoints are close to expected locations
        for i, (y, x) in enumerate(peak_locations[:3]):
            detected_x = keypoints[0, i, 0].item() * (heatmap_size[1] - 1)
            detected_y = keypoints[0, i, 1].item() * (heatmap_size[0] - 1)
            
            self.assertAlmostEqual(detected_x, x, delta=2.0)
            self.assertAlmostEqual(detected_y, y, delta=2.0)
        
        print(f"✓ Keypoint detection accuracy verified")
        print(f"✓ Detected keypoints shape: {keypoints.shape}")
        
    def test_coordinate_conversion_method(self):
        """Test coordinate conversion method"""
        print("\n=== Testing Coordinate Conversion ===")
        
        # Test convert_to_original_coords method
        roi_keypoints = torch.tensor([[[0.5, 0.5], [0.0, 0.0], [1.0, 1.0]]], device=self.device)
        bbox = torch.tensor([0.5, 0.5, 0.4, 0.6], device=self.device)
        
        # Convert to original coordinates
        original_keypoints = self.model.convert_to_original_coords(roi_keypoints, bbox)
        
        # Check shapes
        self.assertEqual(original_keypoints.shape, (1, 3, 2))
        
        # Check that coordinates are within [0, 1]
        self.assertTrue(torch.all(original_keypoints >= 0))
        self.assertTrue(torch.all(original_keypoints <= 1))
        
        print(f"✓ Coordinate conversion verified")
        print(f"✓ Original keypoints: {original_keypoints}")
        
    def test_edge_case_no_persons(self):
        """Test edge case with no persons"""
        print("\n=== Testing No Persons Edge Case ===")
        
        batch = {
            'image': torch.randn(1, 1, 224, 224, device=self.device),
            'bboxes': [torch.zeros(0, 4, device=self.device)]
        }
        
        with torch.no_grad():
            outputs = self.model(batch)
        
        self.assertIn('keypoints', outputs)
        self.assertIn('visibilities', outputs)
        
        # Should handle empty case gracefully
        print("✓ Handled no-person case successfully")
        
    def test_batch_size_consistency(self):
        """Test that model works with different batch sizes"""
        print("\n=== Testing Batch Size Consistency ===")
        
        batch_sizes = [1, 2, 4]
        
        for batch_size in batch_sizes:
            batch = self.create_test_batch_inference_only(
                batch_size=batch_size, 
                num_persons_per_image=[1] * batch_size
            )
            
            with torch.no_grad():
                outputs = self.model(batch)
            
            self.assertEqual(outputs['keypoints'].shape[0], batch_size)
            print(f"✓ Batch size {batch_size}: {outputs['keypoints'].shape}")
        
    def test_different_person_counts(self):
        """Test model with varying number of persons per image"""
        print("\n=== Testing Different Person Counts ===")
        
        test_cases = [
            [1, 1],      # Same number of persons
            [1, 2],      # Different numbers
            [2, 3],      # Multiple persons
            [1, 2, 3],   # Three images with different counts
        ]
        
        for person_counts in test_cases:
            batch = self.create_test_batch_inference_only(
                batch_size=len(person_counts),
                num_persons_per_image=person_counts
            )
            
            with torch.no_grad():
                outputs = self.model(batch)
            
            max_persons = max(person_counts)
            self.assertLessEqual(outputs['keypoints'].shape[1], max_persons)
            
            print(f"✓ Person counts {person_counts}: output shape {outputs['keypoints'].shape}")

def run_performance_benchmark():
    """Run performance benchmark"""
    print("\n" + "="*60)
    print("PERFORMANCE BENCHMARK")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create model
    model_config = ModelConfig(
        backbone=BackboneConfig(in_channels=1, out_channels=128),
        heatmap_head=HeatmapHeadConfig(in_channels=64, num_keypoints=17),
        num_keypoints=17
    )
    training_config = TrainingConfig(pck_thresholds=[0.05, 0.1, 0.2])
    
    model = MultiPersonKeypointModel(model_config, training_config).to(device)
    model.eval()
    
    # Test different scenarios
    test_cases = [
        (1, [1], "Single image, single person"),
        (2, [1, 2], "Two images, 1 and 2 persons"),
        (4, [1, 2, 3, 2], "Four images, varying persons"),
        (1, [5], "Single image, 5 persons"),
    ]
    
    import time
    
    for batch_size, num_persons_list, description in test_cases:
        # Create test batch
        images = torch.randn(batch_size, 1, 224, 224, device=device)
        bboxes = []
        for num_persons in num_persons_list:
            batch_bboxes = torch.rand(num_persons, 4, device=device)
            batch_bboxes[:, 2:] *= 0.5
            batch_bboxes[:, 2:] += 0.2
            bboxes.append(batch_bboxes)
        
        batch = {'image': images, 'bboxes': bboxes}
        
        # Warm up
        with torch.no_grad():
            for _ in range(3):
                _ = model(batch)
        
        # Measure time
        torch.cuda.synchronize() if device.type == 'cuda' else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(10):
                outputs = model(batch)
        
        torch.cuda.synchronize() if device.type == 'cuda' else None
        avg_time = (time.time() - start_time) / 10
        
        total_persons = sum(num_persons_list)
        print(f"{description}: {avg_time:.4f}s avg ({total_persons} persons)")

if __name__ == "__main__":
    print("="*60)
    print("BASIC MODEL TESTS (NO LOSS COMPUTATION)")
    print("="*60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance benchmark
    run_performance_benchmark()
    
    print("\n" + "="*60)
    print("ALL BASIC TESTS COMPLETED")
    print("="*60)
