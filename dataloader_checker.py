#!/usr/bin/env python3
"""
Script đơn giản để kiểm tra đầu ra của dataloader cho keypoint detection.
Sử dụng: python dataloader_checker.py
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# Thêm đường dẫn để import module
sys.path.append(os.path.join(os.path.dirname(__file__), 'keypoint-detection'))

from dll.data.dataloader import OptimizedKeypointsDataset, efficient_collate_fn
from torch.utils.data import DataLoader

def print_tensor_summary(tensor, name, indent=""):
    """In tóm tắt thông tin tensor."""
    if isinstance(tensor, torch.Tensor):
        print(f"{indent}{name}: {tensor.shape} ({tensor.dtype})")
        if tensor.numel() > 0:
            print(f"{indent}  └─ Range: [{tensor.min().item():.3f}, {tensor.max().item():.3f}]")
            if tensor.dtype.is_floating_point:
                print(f"{indent}  └─ Mean: {tensor.mean().item():.3f}")
    elif isinstance(tensor, list):
        print(f"{indent}{name}: List[{len(tensor)}]")
        for i, item in enumerate(tensor[:2]):  # Chỉ hiển thị 2 items đầu
            if isinstance(item, torch.Tensor):
                print(f"{indent}  [{i}]: {item.shape} ({item.dtype})")
            else:
                print(f"{indent}  [{i}]: {type(item).__name__}")
    else:
        print(f"{indent}{name}: {type(tensor).__name__} = {tensor}")

def check_single_sample(dataset_dir):
    """Kiểm tra một sample đơn lẻ."""
    print("🔍 KIỂM TRA SINGLE SAMPLE")
    print("-" * 50)
    
    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True,
        num_keypoints=17,
        heatmap_size=(56, 56),
        max_persons=5,
        enable_caching=True
    )
    
    print(f"📊 Dataset size: {len(dataset)} samples")
    
    # Lấy sample đầu tiên
    sample = dataset[0]
    
    print(f"\n📋 Sample structure:")
    for key, value in sample.items():
        print_tensor_summary(value, key, "  ")
    
    return sample

def check_batch_output(dataset_dir):
    """Kiểm tra batch output từ dataloader."""
    print("\n🔍 KIỂM TRA BATCH OUTPUT")
    print("-" * 50)
    
    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True,
        num_keypoints=17,
        heatmap_size=(56, 56),
        max_persons=5
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=3,
        shuffle=False,
        num_workers=0,
        collate_fn=efficient_collate_fn
    )
    
    print(f"📊 Dataloader: {len(dataloader)} batches")
    
    # Lấy batch đầu tiên
    batch = next(iter(dataloader))
    
    print(f"\n📋 Batch structure:")
    for key, value in batch.items():
        print_tensor_summary(value, key, "  ")
    
    return batch

def analyze_keypoint_data(sample):
    """Phân tích chi tiết dữ liệu keypoint."""
    print("\n🔍 PHÂN TÍCH KEYPOINT DATA")
    print("-" * 50)
    
    heatmaps = sample['heatmaps']
    visibilities = sample['visibilities']
    
    # Phân tích heatmaps
    print(f"🔥 Heatmaps analysis:")
    print(f"  └─ Shape: {heatmaps.shape}")
    print(f"  └─ Non-zero heatmaps: {torch.count_nonzero(torch.sum(heatmaps, dim=(-2, -1))).item()}")
    
    # Phân tích visibilities
    if visibilities.dim() == 3:  # [1, 1, 17]
        vis = visibilities[0, 0]
    else:
        vis = visibilities[0]
    
    print(f"👁️  Visibilities analysis:")
    print(f"  └─ Shape: {vis.shape}")
    print(f"  └─ Visible keypoints: {torch.count_nonzero(vis).item()}")
    print(f"  └─ Visibility values: {vis.unique().tolist()}")
    
    # Tìm keypoints có visibility > 0
    visible_indices = torch.where(vis > 0)[0]
    if len(visible_indices) > 0:
        print(f"  └─ Visible keypoint indices: {visible_indices.tolist()}")

def check_data_consistency(dataset_dir, num_samples=5):
    """Kiểm tra tính nhất quán của dữ liệu."""
    print(f"\n🔍 KIỂM TRA TÍNH NHẤT QUÁN ({num_samples} samples)")
    print("-" * 50)
    
    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True
    )
    
    shapes = {
        'image': [],
        'heatmaps': [],
        'visibilities': [],
        'bboxes': []
    }
    
    num_persons_list = []
    
    for i in range(min(num_samples, len(dataset))):
        try:
            sample = dataset[i]
            
            shapes['image'].append(sample['image'].shape)
            shapes['heatmaps'].append(sample['heatmaps'].shape)
            shapes['visibilities'].append(sample['visibilities'].shape)
            shapes['bboxes'].append(sample['bboxes'].shape)
            num_persons_list.append(sample['num_persons'])
            
            print(f"✅ Sample {i}: {sample['num_persons']} person(s), size {sample['orig_size']}")
            
        except Exception as e:
            print(f"❌ Sample {i}: Error - {e}")
    
    # Kiểm tra tính nhất quán của shapes
    print(f"\n📊 Shape consistency:")
    for key, shape_list in shapes.items():
        unique_shapes = list(set(shape_list))
        if len(unique_shapes) == 1:
            print(f"  ✅ {key}: Consistent shape {unique_shapes[0]}")
        else:
            print(f"  ⚠️  {key}: Inconsistent shapes {unique_shapes}")
    
    # Thống kê số người
    if num_persons_list:
        print(f"\n👥 Person statistics:")
        print(f"  └─ Average: {np.mean(num_persons_list):.2f}")
        print(f"  └─ Range: [{min(num_persons_list)}, {max(num_persons_list)}]")

def main():
    """Hàm main."""
    print("🚀 DATALOADER OUTPUT CHECKER")
    print("=" * 60)
    
    # Đường dẫn dataset
    dataset_dir = "D:/AI/Keypoint_model/minidatasets"
    
    if not Path(dataset_dir).exists():
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    print(f"📁 Dataset path: {dataset_dir}")
    
    try:
        # 1. Kiểm tra single sample
        sample = check_single_sample(dataset_dir)
        
        # 2. Kiểm tra batch output
        batch = check_batch_output(dataset_dir)
        
        # 3. Phân tích keypoint data
        analyze_keypoint_data(sample)
        
        # 4. Kiểm tra tính nhất quán
        check_data_consistency(dataset_dir)
        
        print("\n" + "=" * 60)
        print("✅ TẤT CẢ KIỂM TRA HOÀN THÀNH!")
        print("=" * 60)
        
        # Tóm tắt kết quả
        print(f"\n📋 SUMMARY:")
        print(f"  • Dataset size: {len(sample)} samples")
        print(f"  • Image shape: {sample['image'].shape}")
        print(f"  • Heatmap shape: {sample['heatmaps'].shape}")
        print(f"  • Batch image shape: {batch['image'].shape}")
        print(f"  • Batch heatmap shape: {batch['heatmaps'].shape}")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
