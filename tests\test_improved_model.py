#!/usr/bin/env python3
"""
Test script for improved multi-person keypoint detection model.
Tests 3-class visibility, soft-argmax, coordinate loss, and other improvements.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'keypoint-detection'))

import torch
import torch.nn as nn
import unittest
from pathlib import Path

from dll.models.keypoint_model import MultiPersonKeypointModel
from dll.configs.model_config import ModelConfig, BackboneConfig, HeatmapHeadConfig
from dll.configs.training_config import TrainingConfig, OptimizerConfig, LossConfig
from dll.data.dataloader import OptimizedKeypointsDataset, efficient_collate_fn
from torch.utils.data import DataLoader


class TestImprovedModel(unittest.TestCase):
    """Test improved model features"""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures"""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Testing on device: {cls.device}")

        # Create model config
        cls.model_config = ModelConfig(
            backbone=BackboneConfig(
                in_channels=1,
                out_channels=128,
                input_size=224
            ),
            heatmap_head=HeatmapHeadConfig(
                in_channels=64,
                hidden_channels=64,
                num_keypoints=17,
                heatmap_size=(56, 56)
            ),
            num_keypoints=17
        )

        # Create training config
        cls.training_config = TrainingConfig(
            num_epochs=1,
            batch_size=2,
            optimizer=OptimizerConfig(
                name="adam",
                learning_rate=0.001
            ),
            loss=LossConfig(
                focal_gamma=2.0,
                focal_alpha=0.25,
                learnable_focal_params=True
            ),
            pck_thresholds=[0.002, 0.05, 0.2]
        )

        # Create model
        cls.model = MultiPersonKeypointModel(
            cls.model_config,
            cls.training_config
        )

    def test_3_class_visibility_output(self):
        """Test that model outputs 3-class visibility probabilities"""
        print("\n🧪 Testing 3-class visibility output...")

        # Create dummy input
        batch_size = 2
        dummy_input = {
            'image': torch.randn(batch_size, 1, 224, 224, device=self.device),
            'bboxes': [torch.tensor([[0.5, 0.5, 0.3, 0.4], [0.3, 0.3, 0.2, 0.3]], device=self.device)
                      for _ in range(batch_size)]
        }

        with torch.no_grad():
            outputs = self.model(dummy_input)

        # Check visibility output shape
        visibilities = outputs['visibilities']
        print(f"  └─ Visibility shape: {visibilities.shape}")

        # Should be [B, P, K, 3] for 3-class visibility
        self.assertEqual(visibilities.dim(), 4, "Visibility should be 4D tensor")
        self.assertEqual(visibilities.size(-1), 3, "Last dimension should be 3 for 3-class visibility")

        # Check that probabilities sum to 1 (approximately)
        prob_sums = visibilities.sum(dim=-1)
        self.assertTrue(torch.allclose(prob_sums, torch.ones_like(prob_sums), atol=1e-5),
                       "Visibility probabilities should sum to 1")

        print("  ✅ 3-class visibility test passed!")

    def test_soft_argmax_keypoints(self):
        """Test that soft-argmax produces sub-pixel accurate keypoints"""
        print("\n🧪 Testing soft-argmax keypoint localization...")

        # Create dummy heatmap with known peak
        B, K, H, W = 1, 17, 56, 56
        heatmap = torch.zeros(B, K, H, W, device=self.device)

        # Create a Gaussian peak at (28.5, 28.5) - between pixels
        center_x, center_y = 28.5, 28.5
        sigma = 2.0

        y_coords, x_coords = torch.meshgrid(
            torch.arange(H, device=self.device, dtype=torch.float32),
            torch.arange(W, device=self.device, dtype=torch.float32),
            indexing='ij'
        )

        for k in range(K):
            dist_sq = (x_coords - center_x)**2 + (y_coords - center_y)**2
            heatmap[0, k] = torch.exp(-dist_sq / (2 * sigma**2))

        # Test soft-argmax
        keypoints, visibilities = self.model.decode_heatmap(heatmap)

        print(f"  └─ Keypoints shape: {keypoints.shape}")
        print(f"  └─ Expected center: ({center_x/(W-1):.3f}, {center_y/(H-1):.3f})")
        print(f"  └─ Detected center: ({keypoints[0, 0, 0]:.3f}, {keypoints[0, 0, 1]:.3f})")

        # Check that detected keypoint is close to expected center
        expected_x = center_x / (W - 1)
        expected_y = center_y / (H - 1)

        self.assertAlmostEqual(keypoints[0, 0, 0].item(), expected_x, places=2,
                              msg="X coordinate should be accurate")
        self.assertAlmostEqual(keypoints[0, 0, 1].item(), expected_y, places=2,
                              msg="Y coordinate should be accurate")

        print("  ✅ Soft-argmax test passed!")

    def test_coordinate_loss_computation(self):
        """Test coordinate loss computation"""
        print("\n🧪 Testing coordinate loss computation...")

        # Create dummy data with keypoints
        batch_size = 2
        dummy_batch = {
            'image': torch.randn(batch_size, 1, 224, 224, device=self.device),
            'bboxes': [torch.tensor([[0.5, 0.5, 0.3, 0.4]], device=self.device)
                      for _ in range(batch_size)],
            'keypoints': torch.randn(batch_size, 1, 17, 2, device=self.device),
            'visibilities': torch.randint(0, 3, (batch_size, 1, 17), device=self.device),
            'heatmaps': torch.randn(batch_size, 17, 56, 56, device=self.device)
        }

        # Forward pass
        outputs = self.model(dummy_batch)

        # Check that coordinate loss is computed
        self.assertIn('coordinate_loss', outputs, "Coordinate loss should be computed")
        self.assertIsInstance(outputs['coordinate_loss'], (int, float),
                             "Coordinate loss should be a scalar")

        print(f"  └─ Coordinate loss: {outputs['coordinate_loss']:.6f}")
        print("  ✅ Coordinate loss test passed!")

    def test_visibility_loss_computation(self):
        """Test visibility loss computation"""
        print("\n🧪 Testing visibility loss computation...")

        # Create dummy data
        batch_size = 2
        dummy_batch = {
            'image': torch.randn(batch_size, 1, 224, 224, device=self.device),
            'bboxes': [torch.tensor([[0.5, 0.5, 0.3, 0.4]], device=self.device)
                      for _ in range(batch_size)],
            'keypoints': torch.randn(batch_size, 1, 17, 2, device=self.device),
            'visibilities': torch.randint(0, 3, (batch_size, 1, 17), device=self.device),
            'heatmaps': torch.randn(batch_size, 17, 56, 56, device=self.device)
        }

        # Forward pass
        outputs = self.model(dummy_batch)

        # Check that visibility loss is computed
        self.assertIn('visibility_loss', outputs, "Visibility loss should be computed")
        self.assertIsInstance(outputs['visibility_loss'], (int, float),
                             "Visibility loss should be a scalar")

        print(f"  └─ Visibility loss: {outputs['visibility_loss']:.6f}")
        print("  ✅ Visibility loss test passed!")

    def test_combined_loss_components(self):
        """Test that all loss components are properly combined"""
        print("\n🧪 Testing combined loss components...")

        # Create dummy data
        batch_size = 2
        dummy_batch = {
            'image': torch.randn(batch_size, 1, 224, 224, device=self.device),
            'bboxes': [torch.tensor([[0.5, 0.5, 0.3, 0.4]], device=self.device)
                      for _ in range(batch_size)],
            'keypoints': torch.randn(batch_size, 1, 17, 2, device=self.device),
            'visibilities': torch.randint(0, 3, (batch_size, 1, 17), device=self.device),
            'heatmaps': torch.randn(batch_size, 17, 56, 56, device=self.device)
        }

        # Forward pass
        outputs = self.model(dummy_batch)

        # Check all loss components
        required_losses = ['loss', 'heatmap_loss', 'visibility_loss', 'coordinate_loss', 'total_loss']
        for loss_name in required_losses:
            self.assertIn(loss_name, outputs, f"{loss_name} should be computed")

        print(f"  └─ Total loss: {outputs['loss']:.6f}")
        print(f"  └─ Heatmap loss: {outputs['heatmap_loss']:.6f}")
        print(f"  └─ Visibility loss: {outputs['visibility_loss']:.6f}")
        print(f"  └─ Coordinate loss: {outputs['coordinate_loss']:.6f}")
        print("  ✅ Combined loss test passed!")


def run_integration_test():
    """Run integration test with real data"""
    print("\n🔄 Running integration test with real data...")

    try:
        # Create dataset
        dataset = OptimizedKeypointsDataset(
            dataset_dir="D:/AI/Keypoint_model/minidatasets",
            split="train",
            img_size=224,
            grayscale=True
        )

        # Create dataloader
        dataloader = DataLoader(
            dataset,
            batch_size=2,
            shuffle=False,
            collate_fn=efficient_collate_fn
        )

        # Get one batch
        batch = next(iter(dataloader))

        # Create model
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model_config = ModelConfig(
            backbone=BackboneConfig(in_channels=1, out_channels=128),
            heatmap_head=HeatmapHeadConfig(
                in_channels=64,
                num_keypoints=17,
                heatmap_size=(56, 56)
            ),
            num_keypoints=17
        )

        training_config = TrainingConfig(
            num_epochs=1,
            batch_size=2,
            optimizer=OptimizerConfig(name="adam", learning_rate=0.001),
            loss=LossConfig(focal_gamma=2.0, focal_alpha=0.25),
            pck_thresholds=[0.002, 0.05, 0.2]
        )

        model = MultiPersonKeypointModel(model_config, training_config)

        # Move batch to device
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v
                for k, v in batch.items()}

        # Forward pass
        with torch.no_grad():
            outputs = model(batch)

        print(f"  └─ Batch size: {batch['image'].size(0)}")
        print(f"  └─ Output keys: {list(outputs.keys())}")
        print(f"  └─ Keypoints shape: {outputs['keypoints'].shape}")
        print(f"  └─ Visibilities shape: {outputs['visibilities'].shape}")
        print(f"  └─ Loss: {outputs.get('loss', 'N/A')}")

        print("  ✅ Integration test passed!")

    except Exception as e:
        print(f"  ❌ Integration test failed: {e}")
        return False

    return True


if __name__ == '__main__':
    print("🚀 Testing Improved Multi-Person Keypoint Detection Model")
    print("=" * 60)

    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=0)

    # Run integration test
    run_integration_test()

    print("\n🎉 All tests completed!")
