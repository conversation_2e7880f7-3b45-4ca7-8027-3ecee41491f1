paths:
  default_config: 'D:/AI/Keypoint_model/keypoint-detection/configs/default_config.yaml'
  data_dir: 'D:/AI/Keypoint_model/keypoint-detection/minidatasets'
  output_dir: 'D:/AI/Keypoint_model/keypoint-detection/outputs/training'

model:
  backbone:
    width_mult: 1.0
    in_channels: 1
    out_channels: 128
    input_size: 224
    convert_to_grayscale: true

  person_head:
    in_channels: 128
    num_classes: 1
    conf_threshold: 0.3
    nms_iou_threshold: 0.3
    anchor_sizes: [32, 64, 128]

  keypoint_head:
    in_channels: 128
    num_keypoints: 17
    height: 56
    width: 56
    fine_branch_channels: 64
    regression_channels: 32
    visibility_channels: 32
    dropout_rate: 0.2

  heatmap_head:
    in_channels: 64
    hidden_channels: 64
    num_keypoints: 17
    heatmap_size: [56, 56]  # Match dataloader heatmap size
    dropout_rate: 0.1
    use_attention: true
    num_deconv_layers: 2
    deconv_kernel_sizes: [4, 4]
    deconv_channels: [256, 256]

training:
  num_epochs: 50  # Increase for better training
  batch_size: 4   # Reduce for stability
  num_workers: 2
  save_interval: 10
  validation_interval: 1
  early_stopping_patience: 10
  gradient_clip_norm: 1.0
  pck_thresholds: [0.002, 0.05, 0.2]

  optimizer:
    name: "adam" # hoặc "sgd"
    learning_rate: 0.001
    weight_decay: 0.0003
    momentum: 0.9 # cho SGD
    beta1: 0.9 # cho Adam
    beta2: 0.999 # cho Adam

  augmentation:
    enabled: true   # Enable augmentations for better generalization
    prob: 0.5
    flip:
      enabled: true
      horizontal: true
    rotate:
      enabled: true
      max_angle: 15.0
    scale:
      enabled: true
      range: [0.8, 1.2]

  loss:
    keypoint_loss_weight: 15.0
    visibility_loss_weight: 5.0
    focal_gamma: 2.5
    focal_alpha: 0.25
    learnable_focal_params: true
    label_smoothing: 0.05


