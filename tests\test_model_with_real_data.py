#!/usr/bin/env python3
"""
Test MultiPersonKeypointModel with real data from dataloader
Tests integration between dataloader and model for multi-person scenarios.
"""

import os
import sys
import torch
import numpy as np
import unittest
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "keypoint-detection"))

from dll.models.keypoint_model import MultiPersonKeypointModel
from dll.configs.model_config import ModelConfig, BackboneConfig, HeatmapHeadConfig, PersonDetectionConfig, KeypointHeadConfig
from dll.configs.training_config import TrainingConfig, OptimizerConfig, LossConfig
from dll.data.dataloader import OptimizedKeypointsDataset, efficient_collate_fn
from torch.utils.data import DataLoader

class TestModelWithRealData(unittest.TestCase):
    """Test model with real data from dataloader"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        cls.dataset_dir = "D:/AI/Keypoint_model/minidatasets"
        
        print(f"Running tests on device: {cls.device}")
        print(f"Dataset directory: {cls.dataset_dir}")
        
        # Check if dataset exists
        if not Path(cls.dataset_dir).exists():
            raise unittest.SkipTest(f"Dataset directory not found: {cls.dataset_dir}")
        
        # Create model configuration
        cls.model_config = ModelConfig(
            backbone=BackboneConfig(
                in_channels=1,  # Grayscale
                out_channels=128,
                input_size=224
            ),
            person_head=PersonDetectionConfig(
                in_channels=128,
                num_classes=1
            ),
            keypoint_head=KeypointHeadConfig(
                in_channels=128,
                num_keypoints=17,
                height=56,
                width=56
            ),
            heatmap_head=HeatmapHeadConfig(
                in_channels=64,
                hidden_channels=64,
                num_keypoints=17,
                heatmap_size=(56, 56)
            ),
            num_keypoints=17
        )
        
        cls.training_config = TrainingConfig(
            batch_size=2,
            num_epochs=1,
            optimizer=OptimizerConfig(learning_rate=0.001),
            loss=LossConfig(),
            pck_thresholds=[0.05, 0.1, 0.2]
        )
        
        # Create model
        cls.model = MultiPersonKeypointModel(cls.model_config, cls.training_config).to(cls.device)
        
        # Create dataset and dataloader
        cls.dataset = OptimizedKeypointsDataset(
            dataset_dir=cls.dataset_dir,
            split="train",
            img_size=224,
            grayscale=True,
            num_keypoints=17,
            heatmap_size=(56, 56),
            max_persons=5,
            enable_caching=True
        )
        
        cls.dataloader = DataLoader(
            cls.dataset,
            batch_size=2,
            shuffle=False,
            num_workers=0,
            collate_fn=efficient_collate_fn
        )
        
        print(f"Dataset loaded: {len(cls.dataset)} samples")
        print(f"Dataloader created: {len(cls.dataloader)} batches")
    
    def test_model_forward_with_real_data(self):
        """Test model forward pass with real data"""
        print("\n=== Testing Model Forward with Real Data ===")
        
        # Get a batch from dataloader
        batch = next(iter(self.dataloader))
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        
        # Handle bboxes (list of tensors)
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
        
        print(f"Input batch shapes:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            elif isinstance(value, list):
                print(f"  {key}: List[{len(value)}] - {[v.shape if isinstance(v, torch.Tensor) else type(v) for v in value]}")
        
        # Forward pass
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(batch)
        
        # Check outputs
        self.assertIn('keypoints', outputs)
        self.assertIn('visibilities', outputs)
        self.assertIn('heatmap', outputs)
        
        print(f"Output shapes:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            elif isinstance(value, list):
                print(f"  {key}: List[{len(value)}]")
        
        print("✓ Model forward pass successful with real data")
    
    def test_loss_computation_with_real_data(self):
        """Test loss computation with real data"""
        print("\n=== Testing Loss Computation with Real Data ===")
        
        # Get a batch from dataloader
        batch = next(iter(self.dataloader))
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
        
        # Forward pass with loss computation
        self.model.train()  # Set to training mode for loss computation
        outputs = self.model(batch)
        
        # Check that loss is computed
        if 'loss' in outputs:
            loss_value = outputs['loss'].item()
            self.assertGreater(loss_value, 0)
            self.assertLess(loss_value, 1000)  # Reasonable loss range
            
            print(f"✓ Loss computed: {loss_value:.4f}")
            
            # Check loss components
            if 'heatmap_loss' in outputs:
                print(f"  Heatmap loss: {outputs['heatmap_loss']}")
            if 'total_loss' in outputs:
                print(f"  Total loss: {outputs['total_loss']}")
        else:
            print("⚠ No loss computed (might be due to no valid persons)")
        
        self.model.eval()  # Set back to eval mode
    
    def test_training_step_with_real_data(self):
        """Test training step with real data"""
        print("\n=== Testing Training Step with Real Data ===")
        
        # Get a batch from dataloader
        batch = next(iter(self.dataloader))
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
        
        # Create optimizer
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)
        
        # Perform training step
        self.model.train()
        metrics = self.model.train_step(batch, optimizer)
        
        # Check metrics
        self.assertIsInstance(metrics, dict)
        self.assertIn('loss', metrics)
        
        print(f"Training metrics: {metrics}")
        print("✓ Training step successful with real data")
        
        self.model.eval()
    
    def test_validation_step_with_real_data(self):
        """Test validation step with real data"""
        print("\n=== Testing Validation Step with Real Data ===")
        
        # Get a batch from dataloader
        batch = next(iter(self.dataloader))
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
        
        # Perform validation step
        self.model.eval()
        metrics = self.model.validate_step(batch)
        
        # Check metrics
        self.assertIsInstance(metrics, dict)
        self.assertIn('loss', metrics)
        
        # Check evaluation metrics
        expected_metrics = ['avg_ADE', 'pck_0.05', 'pck_0.1', 'pck_0.2']
        for metric in expected_metrics:
            if metric in metrics:
                self.assertGreaterEqual(metrics[metric], 0)
                if 'pck' in metric:
                    self.assertLessEqual(metrics[metric], 1)
        
        print(f"Validation metrics: {metrics}")
        print("✓ Validation step successful with real data")
    
    def test_multiple_batches(self):
        """Test model with multiple batches"""
        print("\n=== Testing Multiple Batches ===")
        
        batch_count = 0
        total_loss = 0
        total_samples = 0
        
        self.model.eval()
        
        for batch in self.dataloader:
            if batch_count >= 3:  # Test first 3 batches
                break
            
            # Move batch to device
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            if 'bboxes' in batch and isinstance(batch['bboxes'], list):
                batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
            
            # Forward pass
            with torch.no_grad():
                outputs = self.model(batch)
            
            # Check outputs
            self.assertIn('keypoints', outputs)
            
            # Accumulate statistics
            if 'loss' in outputs:
                total_loss += outputs['loss'].item()
            
            total_samples += batch['image'].size(0)
            batch_count += 1
            
            print(f"Batch {batch_count}: {batch['image'].size(0)} samples processed")
        
        avg_loss = total_loss / batch_count if batch_count > 0 else 0
        print(f"✓ Processed {batch_count} batches, {total_samples} total samples")
        print(f"✓ Average loss: {avg_loss:.4f}")
    
    def test_data_format_compatibility(self):
        """Test that model handles dataloader output format correctly"""
        print("\n=== Testing Data Format Compatibility ===")
        
        # Get a batch from dataloader
        batch = next(iter(self.dataloader))
        
        print("Dataloader output format:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} ({value.dtype})")
            elif isinstance(value, list):
                print(f"  {key}: List[{len(value)}]")
                for i, item in enumerate(value[:2]):  # Show first 2 items
                    if isinstance(item, torch.Tensor):
                        print(f"    [{i}]: {item.shape} ({item.dtype})")
                    else:
                        print(f"    [{i}]: {type(item)}")
            else:
                print(f"  {key}: {type(value)}")
        
        # Move to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
        
        # Test model compatibility
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(batch)
        
        # Verify output format
        self.assertIsInstance(outputs, dict)
        required_keys = ['keypoints', 'visibilities', 'heatmap', 'boxes']
        for key in required_keys:
            self.assertIn(key, outputs, f"Missing required output key: {key}")
        
        print("✓ Data format compatibility verified")
    
    def test_gradient_flow(self):
        """Test that gradients flow properly during training"""
        print("\n=== Testing Gradient Flow ===")
        
        # Get a batch from dataloader
        batch = next(iter(self.dataloader))
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(self.device) for bbox in batch['bboxes']]
        
        # Set model to training mode
        self.model.train()
        
        # Forward pass
        outputs = self.model(batch)
        
        if 'loss' in outputs:
            loss = outputs['loss']
            
            # Backward pass
            loss.backward()
            
            # Check that gradients exist
            grad_count = 0
            total_grad_norm = 0
            
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    grad_count += 1
                    total_grad_norm += param.grad.norm().item()
            
            self.assertGreater(grad_count, 0, "No gradients found")
            self.assertGreater(total_grad_norm, 0, "Zero gradient norm")
            
            print(f"✓ Gradients computed for {grad_count} parameters")
            print(f"✓ Total gradient norm: {total_grad_norm:.6f}")
        else:
            print("⚠ No loss to compute gradients")
        
        # Clean up gradients
        self.model.zero_grad()
        self.model.eval()

def run_integration_test():
    """Run integration test with full pipeline"""
    print("\n" + "="*60)
    print("INTEGRATION TEST - FULL PIPELINE")
    print("="*60)
    
    dataset_dir = "D:/AI/Keypoint_model/minidatasets"
    
    if not Path(dataset_dir).exists():
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create configurations
    model_config = ModelConfig(
        backbone=BackboneConfig(in_channels=1, out_channels=128),
        heatmap_head=HeatmapHeadConfig(in_channels=64, num_keypoints=17),
        num_keypoints=17
    )
    training_config = TrainingConfig(pck_thresholds=[0.05, 0.1, 0.2])
    
    # Create model
    model = MultiPersonKeypointModel(model_config, training_config).to(device)
    
    # Create dataloader
    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True,
        max_persons=3
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=2,
        shuffle=False,
        num_workers=0,
        collate_fn=efficient_collate_fn
    )
    
    print(f"Created model and dataloader successfully")
    print(f"Dataset: {len(dataset)} samples, {len(dataloader)} batches")
    
    # Test training loop simulation
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    model.train()
    
    total_loss = 0
    batch_count = 0
    
    for batch in dataloader:
        if batch_count >= 2:  # Test first 2 batches
            break
        
        # Move to device
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        if 'bboxes' in batch and isinstance(batch['bboxes'], list):
            batch['bboxes'] = [bbox.to(device) for bbox in batch['bboxes']]
        
        # Training step
        metrics = model.train_step(batch, optimizer)
        
        total_loss += metrics['loss']
        batch_count += 1
        
        print(f"Batch {batch_count}: Loss = {metrics['loss']:.4f}")
    
    avg_loss = total_loss / batch_count if batch_count > 0 else 0
    print(f"✓ Integration test completed")
    print(f"✓ Average training loss: {avg_loss:.4f}")

if __name__ == "__main__":
    print("="*60)
    print("MODEL + REAL DATA INTEGRATION TESTS")
    print("="*60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    run_integration_test()
    
    print("\n" + "="*60)
    print("ALL INTEGRATION TESTS COMPLETED")
    print("="*60)
