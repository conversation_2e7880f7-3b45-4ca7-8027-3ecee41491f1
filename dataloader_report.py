#!/usr/bin/env python3
"""
Script tạo báo cáo chi tiết về dataloader cho keypoint detection.
Sử dụng: python dataloader_report.py
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from datetime import datetime

# Thêm đường dẫn để import module
sys.path.append(os.path.join(os.path.dirname(__file__), 'keypoint-detection'))

from dll.data.dataloader import OptimizedKeypointsDataset, efficient_collate_fn
from torch.utils.data import DataLoader

def generate_dataloader_report(dataset_dir, output_file="dataloader_report.txt"):
    """Tạo báo cáo chi tiết về dataloader."""
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("DATALOADER OUTPUT ANALYSIS REPORT")
    report_lines.append("=" * 80)
    report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"Dataset Path: {dataset_dir}")
    report_lines.append("")
    
    try:
        # 1. Dataset Information
        report_lines.append("1. DATASET INFORMATION")
        report_lines.append("-" * 40)
        
        dataset = OptimizedKeypointsDataset(
            dataset_dir=dataset_dir,
            split="train",
            img_size=224,
            grayscale=True,
            num_keypoints=17,
            heatmap_size=(56, 56),
            max_persons=5,
            enable_caching=True
        )
        
        report_lines.append(f"• Total samples: {len(dataset)}")
        report_lines.append(f"• Image size: 224x224")
        report_lines.append(f"• Grayscale: True")
        report_lines.append(f"• Number of keypoints: 17")
        report_lines.append(f"• Heatmap size: 56x56")
        report_lines.append(f"• Max persons per image: 5")
        report_lines.append("")
        
        # 2. Single Sample Analysis
        report_lines.append("2. SINGLE SAMPLE ANALYSIS")
        report_lines.append("-" * 40)
        
        sample = dataset[0]
        
        report_lines.append("Sample Structure:")
        for key, value in sample.items():
            if isinstance(value, torch.Tensor):
                report_lines.append(f"  • {key}: {value.shape} ({value.dtype})")
                if value.numel() > 0:
                    report_lines.append(f"    - Range: [{value.min().item():.4f}, {value.max().item():.4f}]")
                    if value.dtype.is_floating_point:
                        report_lines.append(f"    - Mean: {value.mean().item():.4f}")
                    report_lines.append(f"    - Non-zero elements: {torch.count_nonzero(value).item()}")
            else:
                report_lines.append(f"  • {key}: {type(value).__name__}")
        
        report_lines.append("")
        
        # 3. Batch Analysis
        report_lines.append("3. BATCH ANALYSIS")
        report_lines.append("-" * 40)
        
        dataloader = DataLoader(
            dataset,
            batch_size=4,
            shuffle=False,
            num_workers=0,
            collate_fn=efficient_collate_fn
        )
        
        batch = next(iter(dataloader))
        
        report_lines.append(f"• Batch size: 4")
        report_lines.append(f"• Number of batches: {len(dataloader)}")
        report_lines.append("")
        
        report_lines.append("Batch Structure:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                report_lines.append(f"  • {key}: {value.shape} ({value.dtype})")
            elif isinstance(value, list):
                report_lines.append(f"  • {key}: List[{len(value)}]")
                if len(value) > 0 and isinstance(value[0], torch.Tensor):
                    report_lines.append(f"    - Element shape: {value[0].shape}")
        
        report_lines.append("")
        
        # 4. Keypoint Analysis
        report_lines.append("4. KEYPOINT ANALYSIS")
        report_lines.append("-" * 40)
        
        heatmaps = sample['heatmaps']
        visibilities = sample['visibilities']
        
        # Phân tích heatmaps
        report_lines.append("Heatmaps:")
        report_lines.append(f"  • Shape: {heatmaps.shape}")
        report_lines.append(f"  • Active heatmaps: {torch.count_nonzero(torch.sum(heatmaps, dim=(-2, -1))).item()}")
        
        # Phân tích visibilities
        if visibilities.dim() == 3:
            vis = visibilities[0, 0]
        else:
            vis = visibilities[0]
        
        report_lines.append("Visibilities:")
        report_lines.append(f"  • Shape: {vis.shape}")
        report_lines.append(f"  • Visible keypoints: {torch.count_nonzero(vis).item()}")
        report_lines.append(f"  • Unique values: {vis.unique().tolist()}")
        
        visible_indices = torch.where(vis > 0)[0]
        if len(visible_indices) > 0:
            report_lines.append(f"  • Visible indices: {visible_indices.tolist()}")
        
        report_lines.append("")
        
        # 5. Data Consistency Check
        report_lines.append("5. DATA CONSISTENCY CHECK")
        report_lines.append("-" * 40)
        
        num_samples_to_check = min(10, len(dataset))
        shapes = {'image': [], 'heatmaps': [], 'visibilities': [], 'bboxes': []}
        num_persons_list = []
        errors = []
        
        for i in range(num_samples_to_check):
            try:
                sample = dataset[i]
                shapes['image'].append(sample['image'].shape)
                shapes['heatmaps'].append(sample['heatmaps'].shape)
                shapes['visibilities'].append(sample['visibilities'].shape)
                shapes['bboxes'].append(sample['bboxes'].shape)
                num_persons_list.append(sample['num_persons'])
            except Exception as e:
                errors.append(f"Sample {i}: {str(e)}")
        
        report_lines.append(f"Checked {num_samples_to_check} samples:")
        
        # Shape consistency
        for key, shape_list in shapes.items():
            unique_shapes = list(set(shape_list))
            if len(unique_shapes) == 1:
                report_lines.append(f"  ✓ {key}: Consistent shape {unique_shapes[0]}")
            else:
                report_lines.append(f"  ⚠ {key}: Inconsistent shapes {unique_shapes}")
        
        # Person statistics
        if num_persons_list:
            report_lines.append(f"  • Average persons per image: {np.mean(num_persons_list):.2f}")
            report_lines.append(f"  • Person count range: [{min(num_persons_list)}, {max(num_persons_list)}]")
        
        # Errors
        if errors:
            report_lines.append("Errors encountered:")
            for error in errors:
                report_lines.append(f"  ✗ {error}")
        else:
            report_lines.append("  ✓ No errors encountered")
        
        report_lines.append("")
        
        # 6. Performance Metrics
        report_lines.append("6. PERFORMANCE METRICS")
        report_lines.append("-" * 40)
        
        import time
        
        # Measure single sample loading time
        start_time = time.time()
        for i in range(min(5, len(dataset))):
            _ = dataset[i]
        single_sample_time = (time.time() - start_time) / min(5, len(dataset))
        
        # Measure batch loading time
        start_time = time.time()
        _ = next(iter(dataloader))
        batch_time = time.time() - start_time
        
        report_lines.append(f"  • Average single sample loading time: {single_sample_time:.4f}s")
        report_lines.append(f"  • Batch loading time: {batch_time:.4f}s")
        report_lines.append(f"  • Estimated throughput: {len(batch['image']) / batch_time:.2f} samples/sec")
        
        report_lines.append("")
        
        # 7. Recommendations
        report_lines.append("7. RECOMMENDATIONS")
        report_lines.append("-" * 40)
        
        recommendations = []
        
        # Check for shape inconsistencies
        inconsistent_shapes = [key for key, shape_list in shapes.items() 
                             if len(set(shape_list)) > 1]
        if inconsistent_shapes:
            recommendations.append(f"• Fix shape inconsistencies in: {', '.join(inconsistent_shapes)}")
        
        # Check performance
        if single_sample_time > 0.1:
            recommendations.append("• Consider enabling caching to improve loading speed")
        
        if batch_time > 1.0:
            recommendations.append("• Consider using multiple workers for data loading")
        
        # Check data quality
        if errors:
            recommendations.append("• Fix data loading errors before training")
        
        if not recommendations:
            recommendations.append("• Dataloader appears to be working correctly!")
        
        for rec in recommendations:
            report_lines.append(rec)
        
        report_lines.append("")
        report_lines.append("=" * 80)
        report_lines.append("END OF REPORT")
        report_lines.append("=" * 80)
        
    except Exception as e:
        report_lines.append(f"ERROR GENERATING REPORT: {str(e)}")
        import traceback
        report_lines.append(traceback.format_exc())
    
    # Write report to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    # Also print to console
    print('\n'.join(report_lines))
    
    return output_file

def main():
    """Hàm main."""
    dataset_dir = "D:/AI/Keypoint_model/minidatasets"
    
    if not Path(dataset_dir).exists():
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    print("🚀 Generating dataloader report...")
    
    output_file = generate_dataloader_report(dataset_dir)
    
    print(f"\n✅ Report saved to: {output_file}")

if __name__ == "__main__":
    main()
