#!/usr/bin/env python3
"""
Script để kiểm tra đầu ra của dataloader cho keypoint detection.
Sử dụng dataset từ thư mục minidatasets.
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
from pathlib import Path

# Thêm đường dẫn để import module
sys.path.append(os.path.join(os.path.dirname(__file__), 'keypoint-detection'))

from dll.data.dataloader import OptimizedKeypointsDataset, create_optimized_dataloader, efficient_collate_fn

def print_tensor_info(tensor, name):
    """In thông tin chi tiết về tensor."""
    if isinstance(tensor, torch.Tensor):
        print(f"{name}:")
        print(f"  - Shape: {tensor.shape}")
        print(f"  - Dtype: {tensor.dtype}")
        print(f"  - Device: {tensor.device}")
        print(f"  - Min: {tensor.min().item():.4f}")
        print(f"  - Max: {tensor.max().item():.4f}")

        # Chỉ tính mean cho floating point tensors
        if tensor.dtype.is_floating_point:
            print(f"  - Mean: {tensor.mean().item():.4f}")
        else:
            print(f"  - Mean: N/A (integer dtype)")

        print(f"  - Non-zero elements: {torch.count_nonzero(tensor).item()}")
    elif isinstance(tensor, list):
        print(f"{name} (List with {len(tensor)} elements):")
        for i, item in enumerate(tensor):
            if isinstance(item, torch.Tensor):
                print(f"  [{i}] Shape: {item.shape}, Dtype: {item.dtype}")
            else:
                print(f"  [{i}] Type: {type(item)}, Value: {item}")
    else:
        print(f"{name}: {type(tensor)} - {tensor}")

def visualize_sample(sample, save_path=None):
    """Hiển thị một sample với keypoints và heatmaps."""
    image = sample['image']
    heatmaps = sample['heatmaps']
    visibilities = sample['visibilities']
    bboxes = sample['bboxes']

    # Chuyển đổi image để hiển thị
    if image.dim() == 3:  # [C, H, W]
        img_np = image.permute(1, 2, 0).cpu().numpy()
    else:  # [H, W] grayscale
        img_np = image.cpu().numpy()

    # Chuẩn hóa image về [0, 1]
    img_np = (img_np - img_np.min()) / (img_np.max() - img_np.min())

    # Tạo figure với subplots
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f"Sample Analysis - {sample['img_path']}", fontsize=14)

    # Hiển thị ảnh gốc
    axes[0, 0].imshow(img_np, cmap='gray' if len(img_np.shape) == 2 else None)
    axes[0, 0].set_title("Original Image")
    axes[0, 0].axis('off')

    # Hiển thị một số heatmaps quan trọng
    keypoint_names = ['nose', 'left_eye', 'right_eye']
    for i, kp_name in enumerate(keypoint_names):
        if i < min(2, heatmaps.shape[1]):  # Chỉ hiển thị 2 heatmaps để vừa với layout
            heatmap = heatmaps[0, i].cpu().numpy()  # [0] để lấy person đầu tiên
            axes[0, i+1].imshow(heatmap, cmap='hot')
            axes[0, i+1].set_title(f"Heatmap: {kp_name}")
            axes[0, i+1].axis('off')
        elif i+1 < 3:  # Nếu không có đủ heatmaps, để trống
            axes[0, i+1].axis('off')

    # Hiển thị tổng hợp heatmaps
    if heatmaps.shape[1] > 0:
        combined_heatmap = torch.sum(heatmaps[0], dim=0).cpu().numpy()
        axes[1, 0].imshow(combined_heatmap, cmap='hot')
        axes[1, 0].set_title("Combined Heatmaps")
        axes[1, 0].axis('off')

    # Hiển thị visibility pattern
    if visibilities.dim() >= 2:
        if visibilities.dim() == 3:  # [1, 1, 17]
            vis_pattern = visibilities[0, 0].cpu().numpy()  # Lấy person đầu tiên
        else:  # [1, 17]
            vis_pattern = visibilities[0].cpu().numpy()
        axes[1, 1].bar(range(len(vis_pattern)), vis_pattern)
        axes[1, 1].set_title("Keypoint Visibilities")
        axes[1, 1].set_xlabel("Keypoint Index")
        axes[1, 1].set_ylabel("Visibility")

    # Hiển thị bbox info
    bbox_info = f"Num persons: {sample['num_persons']}\n"
    bbox_info += f"Original size: {sample['orig_size']}\n"
    if isinstance(bboxes, list) and len(bboxes) > 0:
        bbox_tensor = bboxes[0]
        bbox_info += f"Bbox shape: {bbox_tensor.shape}\n"
        if bbox_tensor.numel() > 0:
            bbox_info += f"First bbox: {bbox_tensor[0].cpu().numpy()}"
    axes[1, 2].text(0.1, 0.5, bbox_info, transform=axes[1, 2].transAxes,
                    fontsize=10, verticalalignment='center')
    axes[1, 2].set_title("Sample Info")
    axes[1, 2].axis('off')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")
    else:
        plt.show()

    plt.close()

def test_single_sample():
    """Kiểm tra một sample đơn lẻ."""
    print("=" * 60)
    print("KIỂM TRA SINGLE SAMPLE")
    print("=" * 60)

    dataset_dir = "D:/AI/Keypoint_model/minidatasets"

    # Tạo dataset
    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True,
        num_keypoints=17,
        heatmap_size=(56, 56),
        max_persons=5,
        enable_caching=True
    )

    print(f"Dataset size: {len(dataset)}")

    # Lấy một sample
    sample = dataset[0]

    print("\nThông tin chi tiết về sample:")
    print("-" * 40)

    for key, value in sample.items():
        print_tensor_info(value, key)
        print()

    # Visualize sample
    visualize_sample(sample, "sample_visualization.png")

    return sample

def test_dataloader_batch():
    """Kiểm tra batch từ dataloader."""
    print("=" * 60)
    print("KIỂM TRA DATALOADER BATCH")
    print("=" * 60)

    dataset_dir = "D:/AI/Keypoint_model/minidatasets"

    # Tạo dataset và dataloader thủ công để tránh lỗi prefetch_factor
    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True,
        num_keypoints=17,
        heatmap_size=(56, 56),
        max_persons=5
    )

    from torch.utils.data import DataLoader
    dataloader = DataLoader(
        dataset,
        batch_size=2,
        shuffle=False,
        num_workers=0,
        collate_fn=efficient_collate_fn
    )

    print(f"Dataloader length: {len(dataloader)}")

    # Lấy một batch
    batch = next(iter(dataloader))

    print("\nThông tin chi tiết về batch:")
    print("-" * 40)

    for key, value in batch.items():
        print_tensor_info(value, key)
        print()

    return batch

def test_multiple_batches():
    """Kiểm tra nhiều batches để đảm bảo tính nhất quán."""
    print("=" * 60)
    print("KIỂM TRA MULTIPLE BATCHES")
    print("=" * 60)

    dataset_dir = "D:/AI/Keypoint_model/minidatasets"

    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True
    )

    from torch.utils.data import DataLoader
    dataloader = DataLoader(
        dataset,
        batch_size=2,
        shuffle=False,
        num_workers=0,
        collate_fn=efficient_collate_fn
    )

    print("Kiểm tra 3 batches đầu tiên:")

    for i, batch in enumerate(dataloader):
        if i >= 3:
            break

        print(f"\nBatch {i+1}:")
        print(f"  - Image shape: {batch['image'].shape}")
        print(f"  - Heatmaps shape: {batch['heatmaps'].shape}")
        print(f"  - Visibilities shape: {batch['visibilities'].shape}")
        print(f"  - Num persons: {batch['num_persons']}")
        print(f"  - Bboxes type: {type(batch['bboxes'])}")
        if isinstance(batch['bboxes'], list):
            print(f"  - Bboxes[0] shape: {batch['bboxes'][0].shape}")

def analyze_dataset_statistics():
    """Phân tích thống kê của dataset."""
    print("=" * 60)
    print("PHÂN TÍCH THỐNG KÊ DATASET")
    print("=" * 60)

    dataset_dir = "D:/AI/Keypoint_model/minidatasets"

    dataset = OptimizedKeypointsDataset(
        dataset_dir=dataset_dir,
        split="train",
        img_size=224,
        grayscale=True
    )

    num_persons_list = []
    image_sizes = []

    print("Đang phân tích dataset...")

    for i in range(min(len(dataset), 10)):  # Chỉ kiểm tra 10 samples đầu
        try:
            sample = dataset[i]
            num_persons_list.append(sample['num_persons'])
            image_sizes.append(sample['orig_size'])
            print(f"Sample {i}: {sample['num_persons']} persons, size {sample['orig_size']}")
        except Exception as e:
            print(f"Error processing sample {i}: {e}")

    if num_persons_list:
        print(f"\nThống kê số người:")
        print(f"  - Trung bình: {np.mean(num_persons_list):.2f}")
        print(f"  - Min: {np.min(num_persons_list)}")
        print(f"  - Max: {np.max(num_persons_list)}")
        print(f"  - Tổng samples: {len(num_persons_list)}")

def main():
    """Hàm main để chạy tất cả các test."""
    print("KIỂM TRA DATALOADER OUTPUT")
    print("Dataset path: D:/AI/Keypoint_model/minidatasets")
    print()

    try:
        # Test 1: Single sample
        sample = test_single_sample()

        # Test 2: Dataloader batch
        batch = test_dataloader_batch()

        # Test 3: Multiple batches
        test_multiple_batches()

        # Test 4: Dataset statistics
        analyze_dataset_statistics()

        print("\n" + "=" * 60)
        print("TẤT CẢ TESTS HOÀN THÀNH THÀNH CÔNG!")
        print("=" * 60)

    except Exception as e:
        print(f"\nLỖI TRONG QUÁ TRÌNH KIỂM TRA: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
